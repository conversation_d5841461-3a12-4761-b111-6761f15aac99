import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from '@/Components/ui/dialog';
import { Button } from '@/Components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { Badge } from '@/Components/ui/badge';
import { Textarea } from '@/Components/ui/textarea';
import { Label } from '@/Components/ui/label';
import { Checkbox } from '@/Components/ui/checkbox';
import { Card, CardContent } from '@/Components/ui/card';
import { 
  UserPlus, 
  User, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  Building,
  Mail,
  Phone,
  Activity
} from 'lucide-react';
import { router } from '@inertiajs/react';
import { toast } from 'sonner';

interface AssignableUser {
  id: number;
  name: string;
  email: string;
  profile_photo_url?: string | null;
  department?: {
    id: number;
    name: string;
  };
  role?: string;
  workload?: number; // Số tickets đang xử lý
  performance_score?: number; // Điểm hiệu suất
  availability?: 'available' | 'busy' | 'offline';
}

interface SelectedTicket {
  id: string;
  title: string;
  priority: string;
  status: string;
  department?: {
    id: number;
    name: string;
  };
}

interface TicketAssignmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTickets: SelectedTicket[];
  assignableUsers: AssignableUser[];
  onAssignmentComplete?: () => void;
}

export default function TicketAssignmentDialog({
  isOpen,
  onClose,
  selectedTickets,
  assignableUsers,
  onAssignmentComplete
}: TicketAssignmentDialogProps) {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [notes, setNotes] = useState('');
  const [sendNotification, setSendNotification] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const selectedUser = assignableUsers.find(user => user.id === selectedUserId);

  const handleAssign = async () => {
    if (!selectedUserId || selectedTickets.length === 0) {
      toast.error('Vui lòng chọn nhân viên và tickets để giao việc');
      return;
    }

    setIsSubmitting(true);

    try {
      await router.post('/admin/tickets/bulk-assign', {
        ticket_ids: selectedTickets.map(ticket => ticket.id),
        assignee_id: selectedUserId,
        notes: notes,
        send_notification: sendNotification,
      }, {
        onSuccess: () => {
          toast.success(`Đã giao ${selectedTickets.length} ticket(s) cho ${selectedUser?.name}`, {
            style: {
              background: "white",
              color: "black",
            },
          });
          onAssignmentComplete?.();
          onClose();
          resetForm();
        },
        onError: (errors) => {
          console.error('Assignment error:', errors);
          toast.error('Có lỗi xảy ra khi giao việc. Vui lòng thử lại.');
        }
      });
    } catch (error) {
      console.error('Assignment error:', error);
      toast.error('Có lỗi xảy ra khi giao việc. Vui lòng thử lại.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setSelectedUserId(null);
    setNotes('');
    setSendNotification(true);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const getAvailabilityColor = (availability?: string) => {
    switch (availability) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'busy':
        return 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getAvailabilityLabel = (availability?: string) => {
    switch (availability) {
      case 'available':
        return 'Sẵn sàng';
      case 'busy':
        return 'Bận';
      case 'offline':
        return 'Offline';
      default:
        return 'Online';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Giao việc cho nhân viên
          </DialogTitle>
          <DialogDescription>
            Chọn nhân viên để giao {selectedTickets.length} ticket(s) đã chọn
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Selected Tickets Summary */}
          <div>
            <Label className="text-sm font-medium mb-2 block">
              Tickets được chọn ({selectedTickets.length})
            </Label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {selectedTickets.map((ticket) => (
                <Card key={ticket.id} className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium truncate">{ticket.title}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className={getPriorityColor(ticket.priority)}>
                          {ticket.priority}
                        </Badge>
                        {ticket.department && (
                          <Badge variant="secondary" className="text-xs">
                            <Building className="h-3 w-3 mr-1" />
                            {ticket.department.name}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <span className="text-xs text-muted-foreground">#{ticket.id}</span>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* User Selection */}
          <div>
            <Label className="text-sm font-medium mb-3 block">
              Chọn nhân viên ({assignableUsers.length} người có sẵn)
            </Label>
            <div className="grid gap-3 max-h-64 overflow-y-auto">
              {assignableUsers.map((user) => (
                <Card 
                  key={user.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedUserId === user.id 
                      ? 'ring-2 ring-blue-500 bg-blue-50' 
                      : ''
                  }`}
                  onClick={() => setSelectedUserId(user.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.profile_photo_url || ''} alt={user.name} />
                        <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{user.name}</h4>
                          <Badge 
                            variant="outline" 
                            className={getAvailabilityColor(user.availability)}
                          >
                            {getAvailabilityLabel(user.availability)}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {user.email}
                          </span>
                          {user.department && (
                            <span className="flex items-center gap-1">
                              <Building className="h-3 w-3" />
                              {user.department.name}
                            </span>
                          )}
                        </div>

                        <div className="flex items-center gap-4 mt-2">
                          {user.workload !== undefined && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              <Activity className="h-3 w-3 inline mr-1" />
                              {user.workload} tickets
                            </span>
                          )}
                          {user.performance_score !== undefined && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                              <CheckCircle className="h-3 w-3 inline mr-1" />
                              {user.performance_score}% hiệu suất
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="text-right">
                        {selectedUserId === user.id && (
                          <CheckCircle className="h-5 w-5 text-blue-500" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes" className="text-sm font-medium mb-2 block">
              Ghi chú (tùy chọn)
            </Label>
            <Textarea
              id="notes"
              placeholder="Thêm ghi chú về việc giao này..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>

          {/* Options */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="notification"
              checked={sendNotification}
              onCheckedChange={(checked) => setSendNotification(checked as boolean)}
            />
            <Label htmlFor="notification" className="text-sm">
              Gửi thông báo email cho nhân viên được giao
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Hủy
          </Button>
          <Button 
            onClick={handleAssign} 
            disabled={!selectedUserId || selectedTickets.length === 0 || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Đang giao việc...
              </>
            ) : (
              <>
                <UserPlus className="h-4 w-4 mr-2" />
                Giao việc ({selectedTickets.length} tickets)
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
