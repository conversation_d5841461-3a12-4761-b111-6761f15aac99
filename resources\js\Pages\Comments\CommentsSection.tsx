import React, { useRef, useCallback, useEffect } from 'react';
import { CommentsProvider, useComments } from '@/Context/CommentsContext';
import { CommentsResponse, Comment, User } from '@/types/CommentTypes';
import CommentList from '@/Pages/Comments/CommentList';
import CommentForm from '@/Pages/Comments/CommentForm';
import CommentsPagination from '@/Components/CommentsPagination';
import { Headphones } from 'lucide-react';

interface CommentsSectionProps {
  initialComments: CommentsResponse;
  onCommentSubmit: (content: string, parentId?: string) => void;
  postId: string;
  currentUser: User | null;
}

const CommentsContent: React.FC<
  Omit<CommentsSectionProps, 'initialComments'>
> = ({ onCommentSubmit, postId, currentUser }) => {
  const {
    comments,
    pagination,
    isLoading,
    addComment,
    addReply,
    loadPage,
  } = useComments();

  const submittingRef = useRef<boolean>(false);
  const channelRef = useRef<any>(null);

  // Thêm useEffect để debug comments state

  const handleCommentSubmit = useCallback(
    (content: string, parentId?: string) => {
      if (!currentUser || submittingRef.current) return;

      // Prevent duplicate submissions
      submittingRef.current = true;

      try {
        onCommentSubmit(content, parentId);
      } catch (error) {
        console.error('Error submitting comment:', error);
      } finally {
        // Reset submission flag after a delay to prevent rapid submissions
        setTimeout(() => {
          submittingRef.current = false;
        }, 1000);
      }
    },
    [onCommentSubmit, currentUser],
  );

  // Simple real-time updates without optimistic UI
  useEffect(() => {
    if (!window.Echo || !postId) return;

    const channelName = `ticket.${postId}`;
    // console.log('Setting up Echo channel:', channelName);

    try {
      const channel = window.Echo.channel(channelName);
      channelRef.current = channel;

      const handleCommentPosted = (e: { comment: Comment }) => {
        // console.log('New comment received from other user:', e.comment);

        // Add comment via context
        if (e.comment.parent_id) {
          addReply(e.comment.parent_id, e.comment);
        } else {
          addComment(e.comment);
        }
      };

      // Listen for real-time events
      channel.listen('.CommentPosted', handleCommentPosted);

      return () => {
        // console.log('Cleaning up Echo channel:', channelName);
        if (channelRef.current) {
          channelRef.current.stopListening('.CommentPosted');
        }
        channelRef.current = null;
      };
    } catch (error) {
      console.error('Error setting up Echo channel:', error);
    }
  }, [postId, currentUser, addComment, addReply]);



  const commentCount = comments?.length || 0;
  const hasComments = commentCount > 0;

  // Debug comments
  console.log('CommentsSection - comments:', comments);
  console.log('CommentsSection - commentCount:', commentCount);
  console.log('CommentsSection - hasComments:', hasComments);

  return (
    <div className="w-full space-y-1">
      {currentUser ? (
        <div>
          <CommentForm
            onSubmit={content => handleCommentSubmit(content)}
            placeholder="Share your thoughts..."
            buttonText="Send"
          />
        </div>
      ) : (
        <div
          className="p-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900
            rounded-lg text-center border border-gray-200 dark:border-gray-700"
        >
          <div className="flex flex-col space-y-3">
            <div className="flex items-center justify-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <Headphones className="w-6 h-6 text-white" />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
               Đăng nhập để tham gia trao đổi
              </p>
            </div>
          </div>
        </div>
      )}

    
      {hasComments ? (
        <div className="space-y-6">
          <CommentList
            comments={comments}
            onReply={handleCommentSubmit}
            currentUser={currentUser}
          />

          {/* Pagination */}
          {pagination && pagination.last_page > 1 && (
            <div className="flex justify-center mt-6">
              <CommentsPagination
                current_page={pagination.current_page}
                last_page={pagination.last_page}
                next_page_url={pagination.next_page_url}
                prev_page_url={pagination.prev_page_url}
                onPageChange={loadPage}
                isLoading={isLoading}
              />
            </div>
          )}
        </div>
      ) : (
        <div className=" text-gray-500">
          <p>Vui lòng chờ phản hồi!!!  </p>
        </div>
      )}
    </div>
  );
};

const CommentsSection: React.FC<CommentsSectionProps> = ({
  initialComments,
  postId,
  ...props
}) => {
  return (
    <CommentsProvider initialComments={initialComments} postId={postId}>
      <CommentsContent postId={postId} {...props} />
    </CommentsProvider>
  );
};

export default CommentsSection;
