<?php

namespace App\Services;

use App\Data\TransferTicketData;
use App\Models\Departments;
use App\Models\Tag;
use App\Models\Ticket;
use App\Models\User;
use App\Notifications\NewPostNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class TicketTransferService
{
    public function __construct(
        private TicketService $ticketService
    ) {
    }

    /**
     * Create ticket from external transfer using DTO and existing TicketService
     */
    public function createTicketFromTransfer(TransferTicketData $transferData): array
    {
        try {
            DB::beginTransaction();

            // Check for duplicate tickets
            if ($transferData->titleExists()) {
                $existingTicket = $transferData->getExistingTicket();
                return [
                    'success' => false,
                    'message' => 'Ticket with this title already exists',
                    'existing_ticket' => [
                        'id' => $existingTicket->id,
                        'slug' => $existingTicket->slug,
                        'url' => route('tickets.show', $existingTicket->slug)
                    ]
                ];
            }

            // Get or create user
            [$user, $isNewUser, $password] = $transferData->getOrCreateUser();

            // Get or create category
            $category = $transferData->getOrCreateCategory();

            // Create or find tags
            $tags = $transferData->createOrFindTags();

            // Temporarily authenticate as the user for ticket creation
            Auth::login($user);

            // Convert to CreateTicketData and use existing TicketService
            $createTicketData = $transferData->toCreateTicketData($user, $category, $tags);
            
            // Use existing storeTicket method which includes automation
            $ticketResult = $this->ticketService->storeTicket($createTicketData);
            

            // Logout the temporary authentication
            Auth::logout();

            if (!$ticketResult['success']) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => $ticketResult['message'] ?? 'Failed to create ticket'
                ];
            }

            $ticket = $ticketResult['ticket'];

            // Update ticket with transfer-specific data
            $ticket->update([
                'source_system' => $transferData->getSourceSystem(),
                'external_id' => $transferData->getExternalId(),
                'priority' => $transferData->getPriority(),
            ]);

            // Send notifications specific to transfer
            $this->sendTransferNotifications($user, $ticket, $isNewUser, $password);

            DB::commit();

            Log::info('Ticket created successfully via transfer API', [
                'ticket_id' => $ticket->id,
                'user_id' => $user->id,
                'is_new_user' => $isNewUser,
                'source_system' => $transferData->getSourceSystem(),
                'external_id' => $transferData->getExternalId(),
            ]);

            return [
                'success' => true,
                'message' => $isNewUser ? 'Ticket created and user account created' : 'Ticket created successfully',
                'data' => $this->formatTicketResponse($ticket, $user, $isNewUser, $category, $tags)
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Logout if still authenticated
            if (Auth::check()) {
                Auth::logout();
            }
            
            Log::error('Transfer ticket creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transfer_data' => $transferData->toArray()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create ticket: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send notifications specific to transfer tickets
     */
    private function sendTransferNotifications(User $user, Ticket $ticket, bool $isNewUser, ?string $password): void
    {
        // Send email to user
        $this->sendUserNotification($user, $ticket, $isNewUser, $password);

        // Send notifications to staff (already handled by TicketService)
        // Additional transfer-specific notifications can be added here
    }

    /**
     * Send notification email to user
     */
    private function sendUserNotification(User $user, Ticket $ticket, bool $isNewUser, ?string $password): void
    {
        try {
            $ticketUrl = route('tickets.show', $ticket->slug);
            
            if ($isNewUser) {
                Mail::send('emails.new-user-ticket', [
                    'user' => $user,
                    'ticket' => $ticket,
                    'password' => $password,
                    'ticketUrl' => $ticketUrl,
                    'loginUrl' => route('login')
                ], function ($message) use ($user, $ticket) {
                    $message->to($user->email, $user->name)
                           ->subject('Welcome! Your support ticket has been created - ' . $ticket->title);
                });
            } else {
                Mail::send('emails.ticket-created', [
                    'user' => $user,
                    'ticket' => $ticket,
                    'ticketUrl' => $ticketUrl
                ], function ($message) use ($user, $ticket) {
                    $message->to($user->email, $user->name)
                           ->subject('Your support ticket has been created - ' . $ticket->title);
                });
            }
        } catch (\Exception $e) {
            Log::error('Failed to send user notification email', [
                'user_id' => $user->id,
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Format ticket response data
     */
    private function formatTicketResponse(Ticket $ticket, User $user, bool $isNewUser, $category, array $tags): array
    {
        // Reload ticket with all relationships after automation
        $ticket = $ticket->fresh(['user', 'categories', 'tags', 'department', 'assignee']);

        return [
            'ticket' => [
                'id' => $ticket->id,
                'title' => $ticket->title,
                'slug' => $ticket->slug,
                'status' => $ticket->status,
                'priority' => $ticket->priority,
                'category_type' => $ticket->category_type,
                'priority_score' => $ticket->priority_score,
                'url' => route('tickets.show', $ticket->slug),
                'created_at' => $ticket->created_at,
                'auto_assigned' => !is_null($ticket->auto_assigned_at),
                'automation_applied' => $ticket->automation_applied,
                'source_system' => $ticket->source_system,
                'external_id' => $ticket->external_id,
            ],
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'is_new_user' => $isNewUser
            ],
            'assignment' => [
                'department' => $ticket->department ? [
                    'id' => $ticket->department->id,
                    'name' => $ticket->department->name
                ] : null,
                'assignee' => $ticket->assignee ? [
                    'id' => $ticket->assignee->id,
                    'name' => $ticket->assignee->name
                ] : null,
            ],
            'category' => [
                'id' => $category->id,
                'title' => $category->title,
                'type' => $ticket->category_type
            ],
            'tags' => array_map(fn($tag) => [
                'id' => $tag->id,
                'name' => $tag->name
            ], $tags)
        ];
    }
}
