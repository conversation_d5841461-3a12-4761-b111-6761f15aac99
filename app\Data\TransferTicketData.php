<?php

namespace App\Data;

use App\Models\Category;
use App\Models\Tag;
use App\Models\User;
use Spatie\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\In;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Rule;
use Spatie\LaravelData\Attributes\Validation\StringType;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use App\Data\Ticket\CreateTicketData;

class TransferTicketData extends Data
{
    public function __construct(
        #[Required, StringType, Min(5), Max(255)]
        #[Rule('regex:/^[a-zA-Z0-9\s\-_.,!?()]+$/u')]
        public string $title,

        #[Required, StringType, Min(10), Max(10000)]
        public string $content,

        #[Required, StringType, Min(2), Max(100)]
        #[Rule('regex:/^[a-zA-Z0-9\s\-]+$/u')]
        public string $category,

        #[Required, Email]
        #[Rule('email:rfc,dns')]
        #[Max(255)]
        public string $email,

        #[Required, StringType, Min(2), Max(100)]
        #[Rule('regex:/^[a-zA-Z0-9\s\-]+$/u')]
        public string $username,

        #[Nullable, StringType]
        #[In(['low', 'medium', 'high', 'urgent'])]
        public string|Optional $priority = 'medium',

        #[Nullable, StringType, Max(100)]
        public string|Optional $source_system = 'api',

        #[Nullable, StringType, Max(255)]
        public string|Optional $external_id = '',

        #[Nullable, StringType, Max(100)]
        public string|Optional $department = '',

        #[Nullable]
        #[Rule('array', 'max:10')]
        public array|Optional $tags = [],

        #[Nullable, StringType, Max(255)]
        public string|Optional $api_key = '',
    ) {
    }

    /**
     * Get validation messages in Vietnamese
     */
    public static function messages(): array
    {
        return [
            'title.required' => 'Tiêu đề là bắt buộc',
            'title.min' => 'Tiêu đề phải có ít nhất 5 ký tự',
            'title.max' => 'Tiêu đề không được vượt quá 255 ký tự',
            'title.regex' => 'Tiêu đề chứa ký tự không hợp lệ',
            'content.required' => 'Nội dung là bắt buộc',
            'content.min' => 'Nội dung phải có ít nhất 10 ký tự',
            'content.max' => 'Nội dung không được vượt quá 10000 ký tự',
            'category.required' => 'Danh mục là bắt buộc',
            'category.min' => 'Danh mục phải có ít nhất 2 ký tự',
            'category.max' => 'Danh mục không được vượt quá 100 ký tự',
            'category.regex' => 'Danh mục chứa ký tự không hợp lệ',
            'email.required' => 'Email là bắt buộc',
            'email.email' => 'Email không hợp lệ',
            'email.max' => 'Email không được vượt quá 255 ký tự',
            'username.required' => 'Tên người dùng là bắt buộc',
            'username.min' => 'Tên người dùng phải có ít nhất 2 ký tự',
            'username.max' => 'Tên người dùng không được vượt quá 100 ký tự',
            'username.regex' => 'Tên người dùng chứa ký tự không hợp lệ',
            'priority.in' => 'Mức độ ưu tiên không hợp lệ (low, medium, high, urgent)',
            'source_system.max' => 'Hệ thống nguồn không được vượt quá 100 ký tự',
            'external_id.max' => 'ID ngoài không được vượt quá 255 ký tự',
            'department.max' => 'Phòng ban không được vượt quá 100 ký tự',
            'tags.array' => 'Tags phải là một mảng',
            'tags.max' => 'Không được có quá 10 tags',
            'tags.*.string' => 'Mỗi tag phải là chuỗi ký tự',
            'tags.*.max' => 'Mỗi tag không được vượt quá 50 ký tự',
        ];
    }

    /**
     * Additional validation rules for tags array items
     */
    public static function rules(): array
    {
        return [
            'tags.*' => ['string', 'max:50', 'regex:/^[a-zA-Z0-9\s\-_àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ]+$/u'],
        ];
    }

    /**
     * Get or create user from transfer data
     */
    public function getOrCreateUser(): array
    {
        $user = User::where('email', $this->email)->first();
        $isNewUser = false;
        $password = null;

        if (!$user) {
            $password = Str::random(12);
            $user = User::create([
                'name' => $this->username,
                'email' => $this->email,
                'password' => Hash::make($password),
                'email_verified_at' => now(),
                'is_system_user' => false,
            ]);
            $isNewUser = true;
        }

        return [$user, $isNewUser, $password];
    }

    /**
     * Get or create category
     */
    public function getOrCreateCategory(): Category
    {
        return Category::firstOrCreate(
            ['title' => $this->category],
            [
                'description' => 'Category for ' . $this->category . ' product',
                'slug' => Str::slug($this->category)
            ]
        );
    }

    /**
     * Create or find tags with proper slug handling
     */
    public function createOrFindTags(): array
    {
        $tags = [];
        
        // Always add support tag
        $tags[] = Tag::firstOrCreate(
            ['name' => 'support'],
            ['slug' => 'support']
        );
        
        // Add tags from request if provided
        $requestTags = $this->tags instanceof Optional ? [] : $this->tags;
        foreach ($requestTags as $tagName) {
            $cleanTagName = strtolower(trim($tagName));
            $tags[] = Tag::firstOrCreate(
                ['name' => $cleanTagName],
                ['slug' => Str::slug($cleanTagName)]
            );
        }
        
        // Add category-based tag
        $categoryTagName = strtolower(str_replace(' ', '-', $this->category));
        $categoryTag = Tag::firstOrCreate(
            ['name' => $categoryTagName],
            ['slug' => Str::slug($categoryTagName)]
        );
        $tags[] = $categoryTag;

        return array_unique($tags, SORT_REGULAR);
    }

    /**
     * Get priority value (handle Optional)
     */
    public function getPriority(): string
    {
        return $this->priority instanceof Optional ? 'medium' : $this->priority;
    }

    /**
     * Get source system value (handle Optional)
     */
    public function getSourceSystem(): string
    {
        return $this->source_system instanceof Optional ? 'api' : $this->source_system;
    }

    /**
     * Get external ID value (handle Optional)
     */
    public function getExternalId(): ?string
    {
        $value = $this->external_id instanceof Optional ? '' : $this->external_id;
        return empty($value) ? null : $value;
    }

    /**
     * Get tags array (handle Optional)
     */
    public function getTags(): array
    {
        return $this->tags instanceof Optional ? [] : $this->tags;
    }

    /**
     * Get department name (handle Optional)
     */
    public function getDepartmentName(): ?string
    {
        $value = $this->department instanceof Optional ? '' : $this->department;
        return empty($value) ? null : $value;
    }

    /**
     * Convert to CreateTicketData for use with TicketService
     */
    public function toCreateTicketData(User $user, Category $category, array $tags): CreateTicketData
    {
        $externalId = $this->getExternalId();

        return CreateTicketData::from([
            'title' => $this->title,
            'content' => $this->content,
            'categories' => [$category->id],
            'tags' => array_map(fn($tag) => $tag->id, $tags),
            'is_published' => false,
            'priority' => $this->getPriority(),
            'source_system' => $this->getSourceSystem(),
            'external_id' => $externalId ?? '',
        ]);
    }

    /**
     * Check if title already exists
     */
    public function titleExists(): bool
    {
        return \App\Models\Ticket::where('title', $this->title)->exists();
    }

    /**
     * Get existing ticket by title
     */
    public function getExistingTicket(): ?\App\Models\Ticket
    {
        return \App\Models\Ticket::where('title', $this->title)->first();
    }
}
