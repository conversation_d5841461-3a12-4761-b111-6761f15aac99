<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Data\TransferTicketData;
use App\Services\TicketTransferService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Exceptions\ValidationException;
class TransferTicketsController extends Controller
{
    public function __construct(
        private TicketTransferService $ticketTransferService
    ) {
    }

    /**
     * Transfer ticket from external product using DTO validation and existing services
     */
    public function transferTicket(Request $request)
    {
        // Validate API key
        if (!$this->validateApiKey($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid API key'
            ], 401);
        }

        try {
            // Create and validate TransferTicketData DTO
            $transferData = TransferTicketData::from($request->all());

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('TransferTicketData creation failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->except(['api_key'])
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Invalid request data'
            ], 400);
        }

        // Create ticket using service with DTO validation and automation
        $result = $this->ticketTransferService->createTicketFromTransfer($transferData);

        if (!$result['success']) {
            return response()->json($result, $result['message'] === 'Ticket with this title already exists' ? 409 : 500);
        }

        return response()->json($result, 201);
    }

    /**
     * Validate API key
     */
    private function validateApiKey(Request $request): bool
    {
        $apiKey = $request->header('X-API-Key') ?? $request->input('api_key');
        $validApiKey = config('app.transfer_api_key');

        if (!$validApiKey) {
            \Log::warning('Transfer API key not configured in environment');
            return false;
        }

        return hash_equals($validApiKey, $apiKey);
    }





    /**
     * Get API status and information
     */
    public function status(Request $request)
    {
        if (!$this->validateApiKey($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid API key'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'message' => 'Transfer API is working',
            'version' => '1.0',
            'endpoints' => [
                'transfer' => [
                    'method' => 'POST',
                    'url' => route('api.transfer.ticket'),
                    'description' => 'Transfer ticket from external product'
                ],
                'status' => [
                    'method' => 'GET',
                    'url' => route('api.transfer.status'),
                    'description' => 'Check API status'
                ]
            ],
            'required_fields' => [
                'title' => 'string (5-255 chars, supports Vietnamese)',
                'content' => 'string (10-10000 chars)',
                'category' => 'string (2-100 chars, supports Vietnamese)',
                'email' => 'valid email (RFC + DNS validation)',
                'username' => 'string (2-100 chars, supports Vietnamese)'
            ],
            'optional_fields' => [
                'priority' => 'string (low|medium|high|urgent)',
                'source_system' => 'string (max 100 chars)',
                'external_id' => 'string (max 255 chars)',
                'department' => 'string (max 100 chars)',
                'tags' => 'array of strings (max 10 tags, 50 chars each)'
            ],
            'features' => [
                'dto_validation' => 'Laravel Data DTO with comprehensive validation',
                'automation_rules' => 'Automatic assignment based on rules',
                'duplicate_detection' => 'Prevents duplicate tickets by title',
                'user_management' => 'Auto-create users or use existing ones',
                'notification_system' => 'Email notifications for users and staff',
                'audit_trail' => 'Complete tracking of automation applied'
            ],
            'authentication' => 'X-API-Key header or api_key parameter'
        ]);
    }
}
