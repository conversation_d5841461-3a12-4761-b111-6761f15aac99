interface User {
  name: string;
  email: string;
  profile_photo_url: string | null;
}

interface Post {
  id: string;
  title: string;
  status: string;
  priority: string;
  created_at: string;
  user: {
    name: string;
    email: string;
    profile_photo_url: string | null;
  };
  assignee?: {
    id: number;
    name: string;
    email: string;
    profile_photo_url: string | null;
  };
  department?: {
    id: number;
    name: string;
  };
  comment?: number;
  priority_score?: number;
  categories?: Array<{
    id: number;
    title: string;
  }>;
}

interface AutomationStats {
  total_rules: number;
  active_rules: number;
  total_matches: number;
  recent_matches: number;
  top_rules: Array<{
    id: number;
    name: string;
    matched_count: number;
  }>;
}

interface DashboardData {
  ticketStats: {
    urgentTickets: number;
    openTickets: number;
    inProgressTickets: number;
    resolvedTickets: number;
    closedTickets: number;
  };
  tickets: Post[];
  totalTickets: number;
  totalUsers: number;
  automation_stats: AutomationStats;
}
