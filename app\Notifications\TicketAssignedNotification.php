<?php

namespace App\Notifications;

use App\Models\AutomationRule;
use App\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Broadcasting\Channel;

class TicketAssignedNotification extends Notification implements ShouldBroadcast
{
    use Queueable;

    public Ticket $ticket;
    public AutomationRule $rule;

    public function __construct(Ticket $ticket, AutomationRule $rule)
    {
        $this->ticket = $ticket;
        $this->rule = $rule;
    }

    public function via($notifiable): array
    {
        return ['database', 'broadcast'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("Ticket được giao: {$this->ticket->title}")
            ->greeting("Xin chào {$notifiable->name}!")
            ->line("Bạn đã được giao một ticket mới:")
            ->line("**{$this->ticket->title}**")
            ->line("Độ ưu tiên: {$this->ticket->priority}")
            ->line("Trạng thái: {$this->ticket->status}")
            ->line("Được giao bởi rule: {$this->rule->name}")
            ->action('Xem ticket', url('/tickets/'.$this->ticket->slug))
            ->line('Cảm ơn bạn đã sử dụng dịch vụ!');
    }

    public function toArray($notifiable)
    {
        return [
            'ticket_id' => $this->ticket->id,
            'title' => $this->ticket->title,
            'content' => $this->ticket->getExcerpt(),
            'slug' => $this->ticket->slug,
            'message' => "Ticket được giao cho bạn: {$this->ticket->title}",
            'priority' => $this->ticket->priority,
            'status' => $this->ticket->status,
            'name' => $this->ticket->user->name ?? 'Unknown User',
            'profile_photo_url' => $this->ticket->user->profile_photo_url ?? null,
            'categories' => $this->ticket->categories->pluck('title')->toArray(),
            'tags' => $this->ticket->tags->pluck('name')->toArray(),
            'department_name' => $this->ticket->department->name ?? null,
            'assignee_name' => $this->ticket->assignee->name ?? null,
            'rule_name' => $this->rule->name,
            'type_notification' => 'ticket_assigned',
            'created_at' => $this->ticket->created_at->diffForHumans(),
        ];
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'id' => $this->ticket->id,
            'data' => $this->toArray($notifiable),
            'created_at' => $this->ticket->created_at->diffForHumans(),
            'read_at' => null,
            'type' => 'ticket_assigned',
        ]);
    }

    public function broadcastOn()
    {
        return new Channel('notifications');
    }

    public function broadcastAs(): string
    {
        return 'ticket-assigned-notification';
    }
}
