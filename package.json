{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@prettier/plugin-php": "^0.22.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.19", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.6.0", "axios": "^1.10.0", "concurrently": "^9.2.0", "laravel-echo": "^1.19.0", "laravel-vite-plugin": "^1.3.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "pusher-js": "^8.4.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^5.4.19"}, "dependencies": {"@casl/ability": "^6.7.3", "@casl/react": "^5.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.10.0", "@inertiajs/inertia": "^0.11.1", "@inertiajs/react": "^1.3.0", "@mdxeditor/editor": "^3.37.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-heading": "^2.23.0", "@tiptap/extension-link": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "@types/node": "^24.0.6", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.3", "framer-motion": "^12.19.2", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "motion": "^12.19.2", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-hook-form": "^7.59.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-resizable-panels": "^2.1.9", "react-swipeable": "^7.0.2", "recharts": "^2.15.4", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "ziggy-js": "^2.5.3", "zod": "^3.25.67", "zustand": "^5.0.6"}, "prettier": {"semi": true, "singleQuote": true, "useTabs": false, "tabWidth": 2, "trailingComma": "all", "printWidth": 80, "arrowParens": "avoid"}}