<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Ticket;
use App\Models\Category;
use App\Models\Tag;
use App\Models\Departments;
use App\Models\AutomationRule;
use App\Services\AutomationService;
use App\Services\TicketService;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing Automation System...\n";

try {
    DB::beginTransaction();

    // 1. Create test users and departments
    echo "1. Setting up test data...\n";
    
    $user = User::firstOrCreate([
        'email' => '<EMAIL>'
    ], [
        'name' => 'Test User',
        'password' => bcrypt('password'),
    ]);

    $admin = User::firstOrCreate([
        'email' => '<EMAIL>'
    ], [
        'name' => 'Admin User',
        'password' => bcrypt('password'),
    ]);

    // Assign admin role
    if (class_exists('Spatie\Permission\Models\Role')) {
        $adminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
        $admin->assignRole($adminRole);
    }

    $techDept = Departments::firstOrCreate([
        'name' => 'Technical Support'
    ], [
        'description' => 'Technical support department',
    ]);

    // 2. Create automation rules
    echo "2. Creating automation rules...\n";
    
    $urgentRule = AutomationRule::firstOrCreate([
        'name' => 'Urgent Issues Auto-Assignment'
    ], [
        'description' => 'Automatically assign urgent issues to admin with high priority',
        'is_active' => true,
        'execution_order' => 1,
        'conditions' => [
            'title_keywords' => ['urgent', 'critical', 'emergency'],
            'content_keywords' => ['urgent', 'critical', 'emergency']
        ],
        'assigned_priority' => 'urgent',
        'assigned_user_id' => $admin->id,
        'category_type' => 'technical',
    ]);

    $techRule = AutomationRule::firstOrCreate([
        'name' => 'Technical Issues Routing'
    ], [
        'description' => 'Route technical issues to technical department',
        'is_active' => true,
        'execution_order' => 2,
        'conditions' => [
            'title_keywords' => ['bug', 'error', 'technical'],
            'content_keywords' => ['bug', 'error', 'technical']
        ],
        'assigned_priority' => 'medium',
        'assigned_department_id' => $techDept->id,
        'category_type' => 'technical',
    ]);

    // 3. Test urgent ticket creation
    echo "3. Testing urgent ticket creation...\n";
    
    $urgentTicket = Ticket::create([
        'title' => 'URGENT: System is down!',
        'content' => 'This is an urgent critical issue that needs immediate attention.',
        'user_id' => $user->id,
        'slug' => 'urgent-system-down-' . time(),
        'is_published' => true,
        'status' => 'open',
        'priority' => 'medium', // Will be changed by automation
    ]);

    // Apply automation
    $automationService = app(AutomationService::class);
    $appliedRules = $automationService->applyRulesToTicket($urgentTicket);

    $urgentTicket->refresh();

    echo "   - Ticket created: {$urgentTicket->title}\n";
    echo "   - Applied rules: " . count($appliedRules) . "\n";
    echo "   - Final priority: {$urgentTicket->priority}\n";
    echo "   - Assigned to: " . ($urgentTicket->assignee->name ?? 'None') . "\n";

    // 4. Test technical ticket creation
    echo "4. Testing technical ticket creation...\n";
    
    $techTicket = Ticket::create([
        'title' => 'Bug in the system',
        'content' => 'There is a technical error in the application that needs fixing.',
        'user_id' => $user->id,
        'slug' => 'bug-system-' . time(),
        'is_published' => true,
        'status' => 'open',
        'priority' => 'low', // Will be changed by automation
    ]);

    $appliedRules = $automationService->applyRulesToTicket($techTicket);
    $techTicket->refresh();

    echo "   - Ticket created: {$techTicket->title}\n";
    echo "   - Applied rules: " . count($appliedRules) . "\n";
    echo "   - Final priority: {$techTicket->priority}\n";
    echo "   - Assigned department: " . ($techTicket->department->name ?? 'None') . "\n";

    // 5. Test TicketService integration
    echo "5. Testing TicketService integration...\n";
    
    $ticketService = app(TicketService::class);
    
    $ticketData = [
        'title' => 'Payment system critical error',
        'content' => 'The payment system is showing critical errors and needs urgent attention.',
        'categories' => [],
        'tags' => [],
        'product_id' => null,
        'product_name' => null,
    ];

    $result = $ticketService->storeTicket($ticketData, $user);
    
    if ($result['success']) {
        $newTicket = $result['ticket'];
        echo "   - Ticket created via service: {$newTicket->title}\n";
        echo "   - Priority: {$newTicket->priority}\n";
        echo "   - Status: {$newTicket->status}\n";
        echo "   - Assigned to: " . ($newTicket->assignee->name ?? 'None') . "\n";
        echo "   - Department: " . ($newTicket->department->name ?? 'None') . "\n";
    } else {
        echo "   - Failed to create ticket: " . $result['message'] . "\n";
    }

    // 6. Test automation summary
    echo "6. Testing automation summary...\n";
    
    $summary = $automationService->getTicketAutomationSummary($urgentTicket);
    echo "   - Has automation: " . ($summary['has_automation'] ? 'Yes' : 'No') . "\n";
    echo "   - Rules applied: " . $summary['rules_count'] . "\n";

    // 7. Test available rules
    echo "7. Available automation rules:\n";
    $availableRules = $automationService->getAvailableRules();
    foreach ($availableRules as $rule) {
        echo "   - {$rule['name']}: {$rule['description']}\n";
        echo "     Priority: {$rule['actions']['priority']}\n";
        echo "     Department: " . ($rule['actions']['department'] ?? 'None') . "\n";
        echo "     Assignee: " . ($rule['actions']['assignee'] ?? 'None') . "\n";
    }

    DB::rollback(); // Rollback test data
    echo "\nTest completed successfully! (Data rolled back)\n";

} catch (Exception $e) {
    DB::rollback();
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
