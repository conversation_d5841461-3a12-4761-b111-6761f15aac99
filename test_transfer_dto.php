<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Data\TransferTicketData;

// Test basic DTO creation
echo "Testing TransferTicketData DTO...\n\n";

try {
    // Test 1: Basic required fields only
    echo "1. Testing basic required fields:\n";
    $basicData = TransferTicketData::from([
        'title' => 'Test ticket title',
        'content' => 'This is a test ticket content with sufficient length',
        'category' => 'Technical Support',
        'email' => '<EMAIL>',
        'username' => 'Test User'
    ]);
    
    echo "   ✅ Basic DTO created successfully\n";
    echo "   - Title: {$basicData->title}\n";
    echo "   - Priority: {$basicData->getPriority()}\n";
    echo "   - Source System: {$basicData->getSourceSystem()}\n";
    echo "   - External ID: " . ($basicData->getExternalId() ?? 'null') . "\n";
    echo "   - Tags: " . json_encode($basicData->getTags()) . "\n\n";

    // Test 2: With optional fields
    echo "2. Testing with optional fields:\n";
    $fullData = TransferTicketData::from([
        'title' => 'Advanced test ticket',
        'content' => 'This is a more comprehensive test with all optional fields',
        'category' => 'Payment Issues',
        'email' => '<EMAIL>',
        'username' => 'Advanced User',
        'priority' => 'urgent',
        'source_system' => 'external-api',
        'external_id' => 'EXT-12345',
        'department' => 'IT Support',
        'tags' => ['urgent', 'payment', 'api']
    ]);
    
    echo "   ✅ Full DTO created successfully\n";
    echo "   - Title: {$fullData->title}\n";
    echo "   - Priority: {$fullData->getPriority()}\n";
    echo "   - Source System: {$fullData->getSourceSystem()}\n";
    echo "   - External ID: {$fullData->getExternalId()}\n";
    echo "   - Department: {$fullData->getDepartmentName()}\n";
    echo "   - Tags: " . json_encode($fullData->getTags()) . "\n\n";

    // Test 3: Validation errors
    echo "3. Testing validation errors:\n";
    try {
        $invalidData = TransferTicketData::from([
            'title' => 'Hi', // Too short
            'content' => 'Short', // Too short
            'category' => 'A', // Too short
            'email' => 'invalid-email',
            'username' => 'A' // Too short
        ]);
    } catch (\Spatie\LaravelData\Exceptions\ValidationException $e) {
        echo "   ✅ Validation errors caught correctly:\n";
        foreach ($e->errors() as $field => $errors) {
            echo "   - {$field}: " . implode(', ', $errors) . "\n";
        }
    }
    echo "\n";

    // Test 4: Vietnamese characters
    echo "4. Testing Vietnamese characters:\n";
    $vietnameseData = TransferTicketData::from([
        'title' => 'Lỗi thanh toán không thể xử lý được',
        'content' => 'Khách hàng báo cáo không thể hoàn tất giao dịch thanh toán qua thẻ tín dụng',
        'category' => 'Vấn đề thanh toán',
        'email' => '<EMAIL>',
        'username' => 'Nguyễn Văn A'
    ]);
    
    echo "   ✅ Vietnamese characters handled correctly\n";
    echo "   - Title: {$vietnameseData->title}\n";
    echo "   - Category: {$vietnameseData->category}\n";
    echo "   - Username: {$vietnameseData->username}\n\n";

    echo "🎉 All tests passed successfully!\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
