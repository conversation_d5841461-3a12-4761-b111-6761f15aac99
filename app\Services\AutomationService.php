<?php

namespace App\Services;

use App\Models\AutomationRule;
use App\Models\Ticket;
use App\Models\User;
use App\Notifications\TicketAssignedNotification;
use Illuminate\Support\Facades\Log;

class AutomationService
{
    /**
     * Apply automation rules to a ticket
     */
    public function applyRulesToTicket(Ticket $ticket): array
    {
        $appliedRules = [];
        
        try {
            // Get active automation rules ordered by execution priority
            $rules = AutomationRule::active()
                ->orderedByExecution()
                ->with(['assignedDepartment', 'assignedUser'])
                ->get();

            Log::info('Applying automation rules to ticket', [
                'ticket_id' => $ticket->id,
                'ticket_title' => $ticket->title,
                'rules_count' => $rules->count(),
            ]);

            foreach ($rules as $rule) {
                if ($rule->matchesTicket($ticket)) {
                    Log::info('Rule matched ticket', [
                        'rule_id' => $rule->id,
                        'rule_name' => $rule->name,
                        'ticket_id' => $ticket->id,
                    ]);

                    // Apply the rule
                    $rule->applyToTicket($ticket);
                    $appliedRules[] = $rule;

                    // Send notification if ticket was assigned to someone
                    $this->sendAssignmentNotifications($ticket, $rule);
                }
            }

            // Reload ticket to get updated data
            $ticket->refresh();

            Log::info('Automation rules applied successfully', [
                'ticket_id' => $ticket->id,
                'applied_rules_count' => count($appliedRules),
                'final_priority' => $ticket->priority,
                'final_status' => $ticket->status,
                'assigned_to' => $ticket->assignee_id,
                'assigned_department' => $ticket->department_id,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to apply automation rules', [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $appliedRules;
    }

    /**
     * Send notifications when ticket is assigned
     */
    private function sendAssignmentNotifications(Ticket $ticket, AutomationRule $rule): void
    {
        try {
            $notifiedUsers = [];

            // Notify assigned user
            if ($rule->assigned_user_id && $ticket->assignee) {
                $ticket->assignee->notify(new TicketAssignedNotification($ticket, $rule));
                $notifiedUsers[] = $ticket->assignee->id;
                
                Log::info('Assignment notification sent to user', [
                    'ticket_id' => $ticket->id,
                    'user_id' => $ticket->assignee->id,
                    'user_name' => $ticket->assignee->name,
                ]);
            }

            // Notify department members (if assigned to department and not already notified)
            if ($rule->assigned_department_id && $ticket->department) {
                $departmentUsers = $ticket->department->users()
                    ->whereNotIn('id', $notifiedUsers)
                    ->where('id', '!=', $ticket->user_id) // Don't notify ticket creator
                    ->get();

                foreach ($departmentUsers as $user) {
                    $user->notify(new TicketAssignedNotification($ticket, $rule));
                    $notifiedUsers[] = $user->id;
                }

                Log::info('Assignment notifications sent to department', [
                    'ticket_id' => $ticket->id,
                    'department_id' => $ticket->department->id,
                    'department_name' => $ticket->department->name,
                    'notified_users_count' => count($departmentUsers),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to send assignment notifications', [
                'ticket_id' => $ticket->id,
                'rule_id' => $rule->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get automation rules summary for a ticket
     */
    public function getTicketAutomationSummary(Ticket $ticket): array
    {
        $appliedRules = $ticket->automation_applied ?? [];
        
        return [
            'has_automation' => !empty($appliedRules),
            'rules_count' => count($appliedRules),
            'applied_rules' => $appliedRules,
            'auto_assigned_at' => $ticket->auto_assigned_at,
            'auto_assigned_by_rule' => $ticket->autoAssignedByRule,
        ];
    }

    /**
     * Check if a ticket can be auto-assigned
     */
    public function canAutoAssign(Ticket $ticket): bool
    {
        // Don't auto-assign if already assigned manually
        if ($ticket->assignee_id && !$ticket->auto_assigned_at) {
            return false;
        }

        // Don't auto-assign if automation was already applied
        if (!empty($ticket->automation_applied)) {
            return false;
        }

        return true;
    }

    /**
     * Get available automation rules for preview
     */
    public function getAvailableRules(): array
    {
        return AutomationRule::active()
            ->orderedByExecution()
            ->with(['assignedDepartment', 'assignedUser'])
            ->get()
            ->map(function ($rule) {
                return [
                    'id' => $rule->id,
                    'name' => $rule->name,
                    'description' => $rule->description,
                    'conditions' => $rule->conditions,
                    'actions' => [
                        'priority' => $rule->assigned_priority,
                        'category_type' => $rule->category_type,
                        'department' => $rule->assignedDepartment?->name,
                        'assignee' => $rule->assignedUser?->name,
                    ],
                    'execution_order' => $rule->execution_order,
                    'matched_count' => $rule->matched_count,
                    'last_matched_at' => $rule->last_matched_at,
                ];
            })
            ->toArray();
    }
}
