<?php

namespace App\Data\Ticket;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Rule;
use Spatie\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class CreateTicketData extends Data
{
    public function __construct(
        #[Rule('required|string|max:255')]
        public string $title,

        #[Rule('required|string')]
        public string $content,

        #[Rule('required|boolean')]
        public bool $is_published,

        #[Rule('nullable|array')]
        public array $categories = [],

        #[Rule('nullable|array')]
        public array $tags = [],

        #[Nullable, Rule('string|in:low,medium,high,urgent')]
        public string|Optional $priority = 'medium',

        #[Nullable, Rule('string|max:100')]
        public string|Optional $source_system = 'web',

        #[Nullable, Rule('string|max:255')]
        public string|Optional $external_id = '',
    ) {}
}
